import { Sampler, SamplingDecision, SamplingResult, TraceIdRatioBasedSampler } from '@opentelemetry/sdk-trace-base';
import { Context } from '@opentelemetry/api';

export class CustomSampler implements Sampler {
    private ratioSampler: TraceIdRatioBasedSampler;

    constructor() {
        // 使用环境变量设置采样率，默认为0.1
        this.ratioSampler = new TraceIdRatioBasedSampler(Number(process.env.TRACER_SAMPLER_RATIO || 0.1));
    }

    shouldSample(
        context: Context,
        traceId: string,
        spanName: string,
        spanKind?: any,
        attributes?: any,
        links?: any
    ): SamplingResult {
        // 过滤 http 层的 GET /health
        if ((spanName === 'GET' || spanName === 'POST') && attributes && (attributes['http.target'] === '/health' || attributes['http.route'] === '/health')) return { decision: SamplingDecision.NOT_RECORD };
        // 兼容 handler 层
        if (spanName.includes('/health')) return { decision: SamplingDecision.NOT_RECORD };
        // 过滤掉环境变量获取
        if (spanName.startsWith('middleware - ')) return { decision: SamplingDecision.NOT_RECORD };
        if (spanName.startsWith('getEnvMiddleware')) return { decision: SamplingDecision.NOT_RECORD };
        // 进行采样(默认采样率0.1)
        return this.ratioSampler.shouldSample(context, traceId);
    }

    toString(): string {
        return 'CustomSampler';
    }
}