import { NodeSDK } from '@opentelemetry/sdk-node';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { JaegerPropagator } from '@opentelemetry/propagator-jaeger';
import { CompositePropagator, W3CTraceContextPropagator, W3CBaggagePropagator } from '@opentelemetry/core';
import { CustomSampler } from './custom-sampler';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-proto';

const instrumentations = getNodeAutoInstrumentations({
  // 禁用 文件系统追踪
  '@opentelemetry/instrumentation-fs': { enabled: false },
  // 禁用 TCP 连接追踪
  '@opentelemetry/instrumentation-net': { enabled: false },
  // 禁用 DNS 解析追踪
  '@opentelemetry/instrumentation-dns': { enabled: false },
  // 禁用 缓存池
  '@opentelemetry/instrumentation-generic-pool': { enabled: false },
  // 禁用 LRU 缓存
  '@opentelemetry/instrumentation-lru-memoizer': { enabled: false },
  // 禁用 Pino 日志库的日志链路
  '@opentelemetry/instrumentation-pino': { enabled: false },
  // 禁用 Winston 日志库的日志链路
  '@opentelemetry/instrumentation-winston': { enabled: false },
  // 禁用 Memcached 缓存操作的链路
  '@opentelemetry/instrumentation-memcached': { enabled: false },
  // 禁用 Cassandra 数据库操作的链路
  '@opentelemetry/instrumentation-cassandra-driver': { enabled: false },
  // 禁用 Tedious（SQL Server 的 Node.js 驱动）数据库操作的链路
  '@opentelemetry/instrumentation-tedious': { enabled: false },
  // 禁用 Restify 框架的 HTTP 请求/响应链路
  '@opentelemetry/instrumentation-restify': { enabled: false },
  // 禁用 Socket.io 实时通信的链路
  '@opentelemetry/instrumentation-socket.io': { enabled: false },
  // 禁用 Cucumber 行为驱动测试的链路
  '@opentelemetry/instrumentation-cucumber': { enabled: false },
  // 其他 instrumentations 保持默认（enabled: true）
});

const sdk = new NodeSDK({
  serviceName: process.env.TRACER_SERVICE_NAME,
  traceExporter: new OTLPTraceExporter({
    //是否使用内网上报链路追踪
    url: process.env.TRACER_INTERNAL_REPORT == 'Y' ? process.env.HTTP_INTERNAL_TRACES_ENDPOINT : process.env.HTTP_TRACES_ENDPOINT,
    // url: 'http://localhost:14268/api/traces', // 默认即可，Jaeger 采集器支持 OTLP
  }),
  instrumentations: [instrumentations],
  sampler: new CustomSampler(),
  textMapPropagator: new CompositePropagator({
    propagators: [
      new JaegerPropagator(),// 支持uber-trace-id
      new W3CTraceContextPropagator(),// 支持标准的trace context
      new W3CBaggagePropagator(),// 支持baggage传播
    ],
  }),
});

export const Tracer = {
  start: () => {
    // 不开启链路调用
    if (process.env.DISABLE_TRACER_REPORT === 'Y') return Promise.resolve();
    // 环境变量校验
    const errors: string[] = [];
    if (!process.env.TRACER_SERVICE_NAME) errors.push('TRACER_SERVICE_NAME');
    if (process.env.TRACER_INTERNAL_REPORT === 'Y') {
      if (!process.env.HTTP_INTERNAL_TRACES_ENDPOINT) errors.push('HTTP_INTERNAL_TRACES_ENDPOINT');
    } else {
      if (!process.env.HTTP_TRACES_ENDPOINT) errors.push('HTTP_TRACES_ENDPOINT');
    }
    if (errors.length > 0) {
      console.error(`[Tracing] 缺少必要环境变量: ${errors.join(', ')}`);
      // 可以选择抛出异常，也可以直接 return，不启动链路追踪
      return Promise.resolve(); // 或者 throw new Error(...)
    }
    return sdk.start();
  },
  shutdown: () => {
    // 这里无需校验 env，直接优雅关闭
    return sdk.shutdown();
  },
};